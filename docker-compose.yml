services:
  rabbitmq:
    image: rabbitmq:3.11.3-management
    container_name: rating-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: password

  postgres:
    image: postgres:latest
    container_name: rating-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: rating
    volumes:
      - ./scripts/database/initial-script.sql:/initial/initial-script.sql
      - ./scripts/database/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
    command: [ "postgres", "-c", "config_file=/etc/postgresql/postgresql.conf" ]

  wait_for_postgres:
    image: alpine:latest
    container_name: rating-postgres-checker
    depends_on:
      postgres:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      apk add --no-cache make &&
      make migrate-up-after-infra"

networks:
  rating-network:
    driver: bridge
