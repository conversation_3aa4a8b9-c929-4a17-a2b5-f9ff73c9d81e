FROM golang:1.23-alpine as build

WORKDIR /app

COPY . .

# Set ARG for the gitlab_id and gitlab_token
ARG gitlab_id
ARG gitlab_token

# Make the gitlab_id and gitlab_token available in the environment
ENV gitlab_id=${gitlab_id}
ENV gitlab_token=${gitlab_token}

RUN apk update && apk add git

# Turn off GOSUMDB
RUN export GOSUMDB=off

# Set git configurations
RUN git config --global url."https://${gitlab_id}:${gitlab_token}@gitlab.viswalslab.com/".insteadOf "https://gitlab.viswalslab.com/"

RUN CGO_ENABLED=0 go build -o ./bin/server ./cmd/api/main.go

FROM alpine

WORKDIR /app

COPY --from=build /app/bin/server .

EXPOSE 8080

CMD ["./server"]
