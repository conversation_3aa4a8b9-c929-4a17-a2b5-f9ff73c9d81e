package core

const (
	<PERSON>rror<PERSON><PERSON><PERSON><PERSON>ey           string  = "x-actual-error"
	RequeueHeaderKey         string  = "x-requeue-count"
	Retryable                string  = "x-retryable"
	DlqRequeueCount          string  = "x-dlq-requeue-count"
	DeathHeader              string  = "x-death"
	DeathExchange            string  = "exchange"
	DeathQueue               string  = "queue"
	DeathReason              string  = "reason"
	DeathRoutingKey          string  = "routing-key"
	FirstDeathHeaderExchange string  = "x-first-death-exchange"
	FirstDeathQueueName      string  = "x-first-death-queue"
	FirstDeathReason         string  = "x-first-death-reason"
	LastProcessedAt          string  = "x-last-processed-at"
	FailedAt                 string  = "x-failed-at"
	RequeueRoutingKey        string  = "x-requeue-routing-key"
	RequeueExchange          string  = "x-requeue-exchange"
	SourceQueueName          string  = "x-source-queue-name"
	MaxDlqRequeueCount       float64 = 1
	MaxRequeueCount          float64 = 2
)
