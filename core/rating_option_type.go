package core

import (
	"errors"
	"strings"
)

var ErrInvalidOptionType = NewBusinessError("invalid option type")

type OptionType struct {
	value string
}

const (
	OptionTypeOverallExperience  = "overall_experience"
	OptionTypeProfessionalSkills = "professional_skills"
	OptionTypePersonalSkills     = "personal_skills"
	OptionTypeEngagementSkills   = "engagement_skills"
	OptionTypeThankingNote       = "thanking_note"
)

var validOptionTypes = map[string]bool{
	OptionTypeOverallExperience:  true,
	OptionTypeProfessionalSkills: true,
	OptionTypePersonalSkills:     true,
	OptionTypeEngagementSkills:   true,
	OptionTypeThankingNote:       true,
}

func NewOptionType(v string) (*OptionType, error) {
	normalized := strings.TrimSpace(strings.ToLower(v))
	if normalized == "" {
		return nil, errors.Join(ErrInvalidOptionType, errors.New("option type cannot be empty"))
	}

	if !validOptionTypes[normalized] {
		return nil, errors.Join(ErrInvalidOptionType, errors.New("unknown option type: "+v))
	}

	return &OptionType{
		value: normalized,
	}, nil
}

func (o OptionType) Value() string {
	return o.value
}

func (o OptionType) IsValid() bool {
	return validOptionTypes[o.value]
}

func (o OptionType) String() string {
	return o.value
}

// GetAllOptionTypes returns all valid option types
func GetAllOptionTypes() []string {
	types := make([]string, 0, len(validOptionTypes))
	for optionType := range validOptionTypes {
		types = append(types, optionType)
	}
	return types
}
