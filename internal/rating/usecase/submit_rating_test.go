package usecase_test

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"go.uber.org/mock/gomock"
)

// Mock RabbitMQ publisher for testing
type mockRabbitMQPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockRabbitMQPublisherMockRecorder
}

type MockRabbitMQPublisherMockRecorder struct {
	mock *mockRabbitMQPublisher
}

func NewMockRabbitMQPublisher(ctrl *gomock.Controller) *mockRabbitMQPublisher {
	mock := &mockRabbitMQPublisher{ctrl: ctrl}
	mock.recorder = &MockRabbitMQPublisherMockRecorder{mock}
	return mock
}

func (m *mockRabbitMQPublisher) EXPECT() *MockRabbitMQPublisherMockRecorder {
	return m.recorder
}

func (m *mockRabbitMQPublisher) Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", ctx, exchangeName, key, publishing)
	ret0, _ := ret[0].(error)
	return ret0
}

func (mr *MockRabbitMQPublisherMockRecorder) Publish(ctx, exchangeName, key, publishing interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*mockRabbitMQPublisher)(nil).Publish), ctx, exchangeName, key, publishing)
}

func TestSubmitRatingUsecase_Execute(t *testing.T) {
	tests := []struct {
		name      string
		input     *usecase.SubmitRatingInput
		queueName string
		setupMock func(*mockRabbitMQPublisher)
		wantErr   bool
	}{
		{
			name: "success",
			input: &usecase.SubmitRatingInput{
				InteractionID:      "test-interaction-id",
				EventID:            "test-event-id",
				FromRole:           "pro",
				FromEntityID:       "test-from-entity",
				ToRole:             "team_member",
				ToEntityID:         "test-to-entity",
				FieldTag:           "overall_experience",
				StarRating:         5,
				OverallExperience:  "Great experience",
				ProfessionalSkills: "Excellent",
				PersonalSkills:     "Very good",
				ThankingNote:       "Thank you",
				EngagementSkills:   "Outstanding",
				FormData: map[string]interface{}{
					"additional_info": "test data",
				},
			},
			queueName: "test-rating-queue",
			setupMock: func(m *mockRabbitMQPublisher) {
				m.EXPECT().Publish(gomock.Any(), "", "test-rating-queue", gomock.Any()).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "rabbitmq_publish_error",
			input: &usecase.SubmitRatingInput{
				InteractionID: "test-interaction-id",
				EventID:       "test-event-id",
				FromRole:      "pro",
				FromEntityID:  "test-from-entity",
				ToRole:        "team_member",
				ToEntityID:    "test-to-entity",
				FieldTag:      "overall_experience",
				StarRating:    5,
			},
			queueName: "test-rating-queue",
			setupMock: func(m *mockRabbitMQPublisher) {
				m.EXPECT().Publish(gomock.Any(), "", "test-rating-queue", gomock.Any()).Return(assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockPublisher := NewMockRabbitMQPublisher(ctrl)
			tt.setupMock(mockPublisher)

			uc := usecase.NewSubmitRatingUsecase(mockPublisher, tt.queueName)

			ctx := context.Background()
			result, err := uc.Execute(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.True(t, result.Success)
				assert.NotEmpty(t, result.CorrelationID)
				assert.Equal(t, "Rating submitted successfully", result.Message)
			}
		})
	}
}

func TestSubmitRatingUsecase_MessageFormat(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var capturedMessage amqp.Publishing

	mockPublisher := NewMockRabbitMQPublisher(ctrl)
	mockPublisher.EXPECT().Publish(gomock.Any(), "", "test-queue", gomock.Any()).DoAndReturn(
		func(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
			capturedMessage = publishing
			return nil
		})

	uc := usecase.NewSubmitRatingUsecase(mockPublisher, "test-queue")

	input := &usecase.SubmitRatingInput{
		InteractionID:      "test-interaction-id",
		EventID:            "test-event-id",
		FromRole:           "pro",
		FromEntityID:       "test-from-entity",
		ToRole:             "team_member",
		ToEntityID:         "test-to-entity",
		FieldTag:           "overall_experience",
		StarRating:         5,
		OverallExperience:  "Great experience",
		ProfessionalSkills: "Excellent",
		PersonalSkills:     "Very good",
		ThankingNote:       "Thank you",
		EngagementSkills:   "Outstanding",
		FormData: map[string]interface{}{
			"additional_info": "test data",
		},
	}

	ctx := context.Background()
	result, err := uc.Execute(ctx, input)

	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify message format
	assert.Equal(t, "application/json", capturedMessage.ContentType)
	assert.NotEmpty(t, capturedMessage.CorrelationId)
	assert.False(t, capturedMessage.Timestamp.IsZero())

	// Verify message body contains the interaction data
	var messageBody map[string]interface{}
	err = json.Unmarshal(capturedMessage.Body, &messageBody)
	assert.NoError(t, err)

	assert.Equal(t, input.InteractionID, messageBody["interaction_id"])
	assert.Equal(t, input.EventID, messageBody["event_id"])
	assert.Equal(t, input.FromRole, messageBody["from_role"])
	assert.Equal(t, input.FromEntityID, messageBody["from_entity_id"])
	assert.Equal(t, input.ToRole, messageBody["to_role"])
	assert.Equal(t, input.ToEntityID, messageBody["to_entity_id"])
	assert.Equal(t, input.FieldTag, messageBody["field_tag"])
	assert.Equal(t, float64(input.StarRating), messageBody["star_rating"]) // JSON unmarshals numbers as float64
	assert.Equal(t, input.OverallExperience, messageBody["overall_experience"])
	assert.Equal(t, input.ProfessionalSkills, messageBody["professional_skills"])
	assert.Equal(t, input.PersonalSkills, messageBody["personal_skills"])
	assert.Equal(t, input.ThankingNote, messageBody["thanking_note"])
	assert.Equal(t, input.EngagementSkills, messageBody["engagement_skills"])
	assert.NotNil(t, messageBody["form_data"])
	assert.NotEmpty(t, messageBody["submitted_at"])
	assert.NotEmpty(t, messageBody["correlation_id"])
}
