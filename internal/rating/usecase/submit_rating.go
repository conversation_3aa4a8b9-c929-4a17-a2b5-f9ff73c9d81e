package usecase

import (
	"context"
	"encoding/json"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/core"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var submitRatingUsecaseName = "SubmitRatingUsecase"

type rabbitmqPublisher interface {
	Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error
}

type SubmitRatingUsecase struct {
	rabbitmq  rabbitmqPublisher
	queueName string
}

func NewSubmitRatingUsecase(rabbitmq rabbitmqPublisher, queueName string) *SubmitRatingUsecase {
	return &SubmitRatingUsecase{
		rabbitmq:  rabbitmq,
		queueName: queueName,
	}
}

type SubmitRatingInput struct {
	InteractionID      string                 `json:"interaction_id"`
	EventID            string                 `json:"event_id"`
	FromRole           string                 `json:"from_role"`
	FromEntityID       string                 `json:"from_entity_id"`
	ToRole             string                 `json:"to_role"`
	ToEntityID         string                 `json:"to_entity_id"`
	FieldTag           string                 `json:"field_tag"`
	StarRating         int                    `json:"star_rating"`
	OverallExperience  string                 `json:"overall_experience"`
	ProfessionalSkills string                 `json:"professional_skills"`
	PersonalSkills     string                 `json:"personal_skills"`
	ThankingNote       string                 `json:"thanking_note"`
	FormData           map[string]interface{} `json:"form_data"`
	EngagementSkills   string                 `json:"engagement_skills"`
}

type SubmitRatingOutput struct {
	Success       bool   `json:"success"`
	CorrelationID string `json:"correlation_id"`
	Message       string `json:"message"`
}

func (uc *SubmitRatingUsecase) Execute(ctx context.Context, input *SubmitRatingInput) (*SubmitRatingOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", submitRatingUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Generate a correlation ID for this request
	correlationID := core.NewID().Value()

	// Create the interaction payload for RabbitMQ
	interaction := repository.Interaction{
		InteractionID:      input.InteractionID,
		EventID:            input.EventID,
		FromRole:           input.FromRole,
		FromEntityID:       input.FromEntityID,
		ToRole:             input.ToRole,
		ToEntityID:         input.ToEntityID,
		FieldTag:           input.FieldTag,
		StarRating:         input.StarRating,
		OverallExperience:  input.OverallExperience,
		ProfessionalSkills: input.ProfessionalSkills,
		PersonalSkills:     input.PersonalSkills,
		ThankingNote:       input.ThankingNote,
		FormData:           input.FormData,
		SubmittedAt:        time.Now(),
		CorrelationID:      correlationID,
		EngagementSkills:   input.EngagementSkills,
	}

	// Marshal the interaction to JSON
	messageBody, err := json.Marshal(interaction)
	if err != nil {
		logger.Error("failed to marshal interaction", vlog.F("error", err))
		return nil, err
	}

	// Create the AMQP publishing message
	publishing := amqp.Publishing{
		ContentType:   "application/json",
		Body:          messageBody,
		CorrelationId: correlationID,
		Timestamp:     time.Now(),
	}

	// Publish to RabbitMQ queue (using empty exchange name for direct queue publishing)
	err = uc.rabbitmq.Publish(ctx, "", uc.queueName, publishing)
	if err != nil {
		logger.Error("failed to publish rating to rabbitmq", vlog.F("error", err), vlog.F("correlation_id", correlationID))
		return nil, err
	}

	logger.Info("rating submitted successfully", vlog.F("correlation_id", correlationID), vlog.F("interaction_id", input.InteractionID))

	return &SubmitRatingOutput{
		Success:       true,
		CorrelationID: correlationID,
		Message:       "Rating submitted successfully",
	}, nil
}
