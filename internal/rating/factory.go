package rating

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/subscriber"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/ratings/pkg/rabbitmq"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	db       *sqlx.DB
	logger   vlog.Logger
	rabbitmq *rabbitmq.RabbitMQ
}

func NewFactory(db *sqlx.DB, logger vlog.Logger, rabbitmq *rabbitmq.RabbitMQ) *Factory {
	return &Factory{
		db:       db,
		logger:   logger,
		rabbitmq: rabbitmq,
	}
}

func (f *Factory) GetRatingOptionsHandler() *transport.GetRatingOptionsHandler {
	repo := repository.NewRatingRepo(f.db)
	uc := usecase.NewGetRatingOptionsUsecase(repo)
	h := transport.NewGetRatingOptionsHandler(uc)

	return h
}

func (f *Factory) Ratingsubscriber(queueName string) *subscriber.RatingSubscriber {
	sub := subscriber.NewRatingSubscriber(queueName, f.logger)
	sub.SetDB(f.db.DB) //database connection
	return sub
}
