package subscriber

import (
	"context"
	"database/sql"
	"encoding/json"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// Interaction represents the expected JSON payload and DB table
type Interaction = repository.Interaction

type RatingSubscriber struct {
	DB     *sql.DB
	Queue  string
	logger vlog.Logger
}

func NewRatingSubscriber(queueName string, logger vlog.Logger) *RatingSubscriber {
	return &RatingSubscriber{
		Queue:  queueName,
		logger: logger,
	}
}

func (s *RatingSubscriber) QueueName() string {
	return s.Queue
}

// Set the database connection
func (s *RatingSubscriber) SetDB(db *sql.DB) {
	s.DB = db
}

func (s *RatingSubscriber) HandleMessage(ctx context.Context, delivery amqp.Delivery) error {
	s.logger.Info("################---- received rating message --- ######################",
		vlog.F("correlation_id", delivery.CorrelationId),
		vlog.F("queue", s.QueueName()))

	var interaction Interaction
	if err := json.Unmarshal(delivery.Body, &interaction); err != nil {
		s.logger.Error("Failed to unmarshal interaction", vlog.F("error", err))
		return err
	}

	if interaction.CorrelationID == "" {
		interaction.CorrelationID = delivery.CorrelationId
	}

	return repository.InsertInteractionWithLogBook(ctx, s.DB, &interaction)
}
