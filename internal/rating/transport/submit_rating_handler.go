package transport

import (
	"context"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
)

type submitRatingUsecase interface {
	Execute(ctx context.Context, input *usecase.SubmitRatingInput) (*usecase.SubmitRatingOutput, error)
}

type SubmitRating<PERSON>and<PERSON> struct {
	usecase submitRatingUsecase
}

func NewSubmitRatingHandler(usecase submitRatingUsecase) *SubmitRatingHandler {
	return &SubmitRatingHandler{
		usecase: usecase,
	}
}

type SubmitRatingRequest struct {
	InteractionID      string                 `json:"interaction_id" validate:"required"`
	EventID            string                 `json:"event_id" validate:"required"`
	FromRole           string                 `json:"from_role" validate:"required"`
	FromEntityID       string                 `json:"from_entity_id" validate:"required"`
	ToRole             string                 `json:"to_role" validate:"required"`
	ToEntityID         string                 `json:"to_entity_id" validate:"required"`
	FieldTag           string                 `json:"field_tag" validate:"required"`
	StarRating         int                    `json:"star_rating" validate:"required,min=1,max=5"`
	OverallExperience  string                 `json:"overall_experience"`
	ProfessionalSkills string                 `json:"professional_skills"`
	PersonalSkills     string                 `json:"personal_skills"`
	ThankingNote       string                 `json:"thanking_note"`
	FormData           map[string]interface{} `json:"form_data"`
	EngagementSkills   string                 `json:"engagement_skills"`
}

type SubmitRatingResponse struct {
	Success       bool   `json:"success"`
	CorrelationID string `json:"correlation_id"`
	Message       string `json:"message"`
}

func (h *SubmitRatingHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input SubmitRatingRequest
		if err := c.Bind(&input); err != nil {
			return err
		}

		// Validate required fields
		if input.InteractionID == "" {
			return c.JSON(400, map[string]string{"error": "interaction_id is required"})
		}
		if input.EventID == "" {
			return c.JSON(400, map[string]string{"error": "event_id is required"})
		}
		if input.FromRole == "" {
			return c.JSON(400, map[string]string{"error": "from_role is required"})
		}
		if input.FromEntityID == "" {
			return c.JSON(400, map[string]string{"error": "from_entity_id is required"})
		}
		if input.ToRole == "" {
			return c.JSON(400, map[string]string{"error": "to_role is required"})
		}
		if input.ToEntityID == "" {
			return c.JSON(400, map[string]string{"error": "to_entity_id is required"})
		}
		if input.FieldTag == "" {
			return c.JSON(400, map[string]string{"error": "field_tag is required"})
		}

		// Validate star rating range (1-5)
		if input.StarRating < 1 || input.StarRating > 5 {
			return c.JSON(400, map[string]string{"error": "star_rating must be between 1 and 5"})
		}

		usecaseInput := &usecase.SubmitRatingInput{
			InteractionID:      input.InteractionID,
			EventID:            input.EventID,
			FromRole:           input.FromRole,
			FromEntityID:       input.FromEntityID,
			ToRole:             input.ToRole,
			ToEntityID:         input.ToEntityID,
			FieldTag:           input.FieldTag,
			StarRating:         input.StarRating,
			OverallExperience:  input.OverallExperience,
			ProfessionalSkills: input.ProfessionalSkills,
			PersonalSkills:     input.PersonalSkills,
			ThankingNote:       input.ThankingNote,
			FormData:           input.FormData,
			EngagementSkills:   input.EngagementSkills,
		}

		result, err := h.usecase.Execute(ctx, usecaseInput)
		if err != nil {
			return err
		}

		response := &SubmitRatingResponse{
			Success:       result.Success,
			CorrelationID: result.CorrelationID,
			Message:       result.Message,
		}

		return c.JSON(200, response)
	}
}
