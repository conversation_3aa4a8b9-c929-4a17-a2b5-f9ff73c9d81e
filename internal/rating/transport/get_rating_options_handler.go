package transport

import (
	"context"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
)

type getRatingOptionsUsecase interface {
	Execute(ctx context.Context, input *usecase.GetRatingOptionsInput) (*usecase.GetRatingOptionsOutput, error)
}

type GetRatingOptionsHandler struct {
	usecase getRatingOptionsUsecase
}

func NewGetRatingOptionsHandler(usecase getRatingOptionsUsecase) *GetRatingOptionsHandler {
	return &GetRatingOptionsHandler{
		usecase: usecase,
	}
}

type GetRatingOptionsRequest struct {
	OptionType        *string  `json:"option_type"`
	ApplicableToRoles []string `json:"applicable_to_roles" validate:"required,min=1"`
	MinStars          *int     `json:"min_stars" validate:"required,min=1,max=5"`
	FieldTag          *string  `json:"field_tag"`
}

type RatingOptionModel struct {
	ID           int     `json:"id"`
	OptionType   string  `json:"option_type"`
	Value        string  `json:"value"` // Keep JSON field as "value" for API compatibility
	FieldTag     *string `json:"field_tag"`
	DisplayOrder int     `json:"display_order"`
}

type GetRatingOptionsResponse struct {
	Data []*RatingOptionModel `json:"data"`
}

func (h *GetRatingOptionsHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input GetRatingOptionsRequest
		if err := c.Bind(&input); err != nil {
			return err
		}

		// Validate mandatory fields
		if len(input.ApplicableToRoles) == 0 {
			return c.JSON(400, map[string]string{"error": "applicable to roles is required"})
		}
		if input.MinStars == nil {
			return c.JSON(400, map[string]string{"error": "stars is required"})
		}

		// Validate minStars range (1-5)
		if *input.MinStars < 1 || *input.MinStars > 5 {
			return c.JSON(400, map[string]string{"error": "stars must be between 1 and 5"})
		}

		usecaseInput := &usecase.GetRatingOptionsInput{
			OptionType:        input.OptionType,
			ApplicableToRoles: input.ApplicableToRoles,
			MinStars:          *input.MinStars,
			FieldTag:          input.FieldTag,
		}

		options, err := h.usecase.Execute(ctx, usecaseInput)
		if err != nil {
			return err
		}

		result := parseRatingOptionsResponse(options)
		return c.JSON(200, result)
	}
}

func parseRatingOptionsResponse(input *usecase.GetRatingOptionsOutput) *GetRatingOptionsResponse {
	var options []*RatingOptionModel
	for _, option := range input.Data {
		options = append(options, &RatingOptionModel{
			ID:           option.ID,
			OptionType:   option.OptionType,
			Value:        option.Value,
			FieldTag:     option.FieldTag,
			DisplayOrder: option.DisplayOrder,
		})
	}
	return &GetRatingOptionsResponse{Data: options}
}
