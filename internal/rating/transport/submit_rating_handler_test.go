package transport_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"go.uber.org/mock/gomock"
)

// Mock usecase for testing
type mockSubmitRatingUsecase struct {
	ctrl     *gomock.Controller
	recorder *MockSubmitRatingUsecaseMockRecorder
}

type MockSubmitRatingUsecaseMockRecorder struct {
	mock *mockSubmitRatingUsecase
}

func NewMockSubmitRatingUsecase(ctrl *gomock.Controller) *mockSubmitRatingUsecase {
	mock := &mockSubmitRatingUsecase{ctrl: ctrl}
	mock.recorder = &MockSubmitRatingUsecaseMockRecorder{mock}
	return mock
}

func (m *mockSubmitRatingUsecase) EXPECT() *MockSubmitRatingUsecaseMockRecorder {
	return m.recorder
}

func (m *mockSubmitRatingUsecase) Execute(ctx context.Context, input *usecase.SubmitRatingInput) (*usecase.SubmitRatingOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, input)
	ret0, _ := ret[0].(*usecase.SubmitRatingOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockSubmitRatingUsecaseMockRecorder) Execute(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*mockSubmitRatingUsecase)(nil).Execute), ctx, input)
}

func TestSubmitRatingHandler_Handle(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
		setupMock      func(*mockSubmitRatingUsecase)
	}{
		{
			name: "success",
			requestBody: map[string]interface{}{
				"interaction_id":      "test-interaction-id",
				"event_id":            "test-event-id",
				"from_role":           "pro",
				"from_entity_id":      "test-from-entity",
				"to_role":             "team_member",
				"to_entity_id":        "test-to-entity",
				"field_tag":           "overall_experience",
				"star_rating":         5,
				"overall_experience":  "Great experience",
				"professional_skills": "Excellent",
				"personal_skills":     "Very good",
				"thanking_note":       "Thank you",
				"engagement_skills":   "Outstanding",
				"form_data": map[string]interface{}{
					"additional_info": "test data",
				},
			},
			expectedStatus: 200,
			setupMock: func(m *mockSubmitRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.SubmitRatingOutput{
					Success:       true,
					CorrelationID: "test-correlation-id",
					Message:       "Rating submitted successfully",
				}, nil)
			},
		},
		{
			name: "missing_interaction_id",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "pro",
				"from_entity_id": "test-from-entity",
				"to_role":        "team_member",
				"to_entity_id":   "test-to-entity",
				"field_tag":      "overall_experience",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "interaction_id is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "invalid_star_rating_too_low",
			requestBody: map[string]interface{}{
				"interaction_id": "test-interaction-id",
				"event_id":       "test-event-id",
				"from_role":      "pro",
				"from_entity_id": "test-from-entity",
				"to_role":        "team_member",
				"to_entity_id":   "test-to-entity",
				"field_tag":      "overall_experience",
				"star_rating":    0,
			},
			expectedStatus: 400,
			expectedError:  "star_rating must be between 1 and 5",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "invalid_star_rating_too_high",
			requestBody: map[string]interface{}{
				"interaction_id": "test-interaction-id",
				"event_id":       "test-event-id",
				"from_role":      "pro",
				"from_entity_id": "test-from-entity",
				"to_role":        "team_member",
				"to_entity_id":   "test-to-entity",
				"field_tag":      "overall_experience",
				"star_rating":    6,
			},
			expectedStatus: 400,
			expectedError:  "star_rating must be between 1 and 5",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUsecase := NewMockSubmitRatingUsecase(ctrl)
			tt.setupMock(mockUsecase)

			handler := transport.NewSubmitRatingHandler(mockUsecase)

			e := echo.New()

			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/submit-rating", bytes.NewReader(requestBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Handle()(c)

			if tt.expectedStatus == 200 {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response transport.SubmitRatingResponse
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.NotEmpty(t, response.CorrelationID)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var errorResponse map[string]string
				err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedError, errorResponse["error"])
			}
		})
	}
}
