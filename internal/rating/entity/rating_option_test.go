package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRatingOption(t *testing.T) {
	tests := []struct {
		name        string
		input       *NewRatingOptionInput
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid rating option",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "Excellent service",
				ApplicableToRoles: []string{"user", "pro"},
				MinStars:          intPtr(4),
				FieldTag:          stringPtr("service"),
				DisplayOrder:      1,
			},
			expectError: false,
		},
		{
			name:        "nil input",
			input:       nil,
			expectError: true,
			errorMsg:    "input cannot be nil",
		},
		{
			name: "invalid option type",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "invalid_type",
				Value:             "Test",
				ApplicableToRoles: []string{"user"},
				DisplayOrder:      1,
			},
			expectError: true,
			errorMsg:    "unknown option type",
		},
		{
			name: "empty value",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "",
				ApplicableToRoles: []string{"user"},
				DisplayOrder:      1,
			},
			expectError: true,
			errorMsg:    "value cannot be empty",
		},
		{
			name: "invalid role",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "Test",
				ApplicableToRoles: []string{"invalid_role"},
				DisplayOrder:      1,
			},
			expectError: true,
			errorMsg:    "unknown entity role",
		},

		{
			name: "invalid min stars",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "Test",
				ApplicableToRoles: []string{"user"},
				MinStars:          intPtr(10), // Invalid: > 5
				DisplayOrder:      1,
			},
			expectError: true,
			errorMsg:    "rating must be between 0-5",
		},
		{
			name: "negative display order",
			input: &NewRatingOptionInput{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "Test",
				ApplicableToRoles: []string{"user"},
				DisplayOrder:      -1,
			},
			expectError: true,
			errorMsg:    "display order cannot be negative",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := NewRatingOption(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify the entity was created correctly
				assert.Equal(t, tt.input.ID, result.ID())
				assert.Equal(t, tt.input.OptionType, result.OptionType())
				assert.Equal(t, tt.input.Value, result.Value())
				assert.Equal(t, tt.input.ApplicableToRoles, result.ApplicableToRoles())
				assert.Equal(t, tt.input.DisplayOrder, result.DisplayOrder())
			}
		})
	}
}

func TestRatingOption_BusinessLogic(t *testing.T) {
	// Create a test rating option
	input := &NewRatingOptionInput{
		ID:                1,
		OptionType:        "overall_experience",
		Value:             "Excellent service",
		ApplicableToRoles: []string{"user", "pro"},
		MinStars:          intPtr(4),
		FieldTag:          stringPtr("service"),
		DisplayOrder:      1,
	}

	ratingOption, err := NewRatingOption(input)
	require.NoError(t, err)

	t.Run("IsApplicableToRole", func(t *testing.T) {
		assert.True(t, ratingOption.IsApplicableToRole("user"))
		assert.True(t, ratingOption.IsApplicableToRole("pro"))
		assert.False(t, ratingOption.IsApplicableToRole("branch"))
		assert.False(t, ratingOption.IsApplicableToRole("invalid_role"))
	})

	t.Run("IsApplicableToStarRating", func(t *testing.T) {
		assert.True(t, ratingOption.IsApplicableToStarRating(4))
		assert.True(t, ratingOption.IsApplicableToStarRating(5))
		assert.False(t, ratingOption.IsApplicableToStarRating(3))
		assert.False(t, ratingOption.IsApplicableToStarRating(1))
	})

	t.Run("IsApplicableToFieldTag", func(t *testing.T) {
		assert.True(t, ratingOption.IsApplicableToFieldTag(stringPtr("service")))
		assert.False(t, ratingOption.IsApplicableToFieldTag(stringPtr("other")))
		assert.False(t, ratingOption.IsApplicableToFieldTag(nil))
	})

	t.Run("Matches", func(t *testing.T) {
		// Should match: correct role, stars, and field tag
		assert.True(t, ratingOption.Matches("user", 4, stringPtr("service")))
		assert.True(t, ratingOption.Matches("pro", 5, stringPtr("service")))

		// Should not match: wrong role
		assert.False(t, ratingOption.Matches("branch", 4, stringPtr("service")))

		// Should not match: insufficient stars
		assert.False(t, ratingOption.Matches("user", 3, stringPtr("service")))

		// Should not match: wrong field tag
		assert.False(t, ratingOption.Matches("user", 4, stringPtr("other")))
	})
}

func TestRatingOption_NoRestrictions(t *testing.T) {
	// Create a rating option with no restrictions
	input := &NewRatingOptionInput{
		ID:                1,
		OptionType:        "overall_experience",
		Value:             "General feedback",
		ApplicableToRoles: []string{"user", "pro", "branch"},
		MinStars:          nil, // No minimum stars
		FieldTag:          nil, // No field tag restrictions
		DisplayOrder:      1,
	}

	ratingOption, err := NewRatingOption(input)
	require.NoError(t, err)

	t.Run("applies to all star ratings when no minimum", func(t *testing.T) {
		assert.True(t, ratingOption.IsApplicableToStarRating(1))
		assert.True(t, ratingOption.IsApplicableToStarRating(3))
		assert.True(t, ratingOption.IsApplicableToStarRating(5))
	})

	t.Run("applies to all field tags when no restrictions", func(t *testing.T) {
		assert.True(t, ratingOption.IsApplicableToFieldTag(nil))
		assert.True(t, ratingOption.IsApplicableToFieldTag(stringPtr("any_tag")))
	})
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func stringPtr(s string) *string {
	return &s
}
