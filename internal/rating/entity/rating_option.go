package entity

import (
	"errors"

	"gitlab.viswalslab.com/backend/ratings/core"
)

var (
	ErrInvalidRatingOption = core.NewBusinessError("invalid rating option")
)

type RatingOption struct {
	id                int
	optionType        core.OptionType
	value             string
	applicableToRoles []core.EntityRole
	minStars          *core.Rating
	fieldTag          core.FieldTag
	displayOrder      int
}

type NewRatingOptionInput struct {
	ID                int
	OptionType        string
	Value             string
	ApplicableToRoles []string
	MinStars          *int
	FieldTag          *string
	DisplayOrder      int
}

func NewRatingOption(input *NewRatingOptionInput) (*RatingOption, error) {
	if input == nil {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("input cannot be nil"))
	}

	// Validate and create option type
	optionType, err := core.NewOptionType(input.OptionType)
	if err != nil {
		return nil, errors.Join(ErrInvalidRatingOption, err)
	}

	// Validate value
	if input.Value == "" {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("value cannot be empty"))
	}

	// Validate and create applicable roles
	var applicableToRoles []core.EntityRole
	for _, roleStr := range input.ApplicableToRoles {
		role, err := core.NewEntityRole(roleStr)
		if err != nil {
			return nil, errors.Join(ErrInvalidRatingOption, err)
		}
		applicableToRoles = append(applicableToRoles, *role)
	}

	// Validate and create min stars
	var minStars *core.Rating
	if input.MinStars != nil {
		rating, err := core.NewRating(float32(*input.MinStars))
		if err != nil {
			return nil, errors.Join(ErrInvalidRatingOption, err)
		}
		minStars = rating
	}

	// Validate and create field tag
	fieldTag, err := core.NewFieldTag(input.FieldTag)
	if err != nil {
		return nil, errors.Join(ErrInvalidRatingOption, err)
	}

	// Validate display order
	if input.DisplayOrder < 0 {
		return nil, errors.Join(ErrInvalidRatingOption, errors.New("display order cannot be negative"))
	}

	return &RatingOption{
		id:                input.ID,
		optionType:        *optionType,
		value:             input.Value,
		applicableToRoles: applicableToRoles,
		minStars:          minStars,
		fieldTag:          *fieldTag,
		displayOrder:      input.DisplayOrder,
	}, nil
}

// Getter methods
func (r RatingOption) ID() int {
	return r.id
}

func (r RatingOption) OptionType() string {
	return r.optionType.Value()
}

func (r RatingOption) Value() string {
	return r.value
}

func (r RatingOption) ApplicableToRoles() []string {
	roles := make([]string, len(r.applicableToRoles))
	for i, role := range r.applicableToRoles {
		roles[i] = role.Value()
	}
	return roles
}

func (r RatingOption) MinStars() *int {
	if r.minStars == nil {
		return nil
	}
	stars := int(r.minStars.Value())
	return &stars
}

func (r RatingOption) FieldTag() *string {
	return r.fieldTag.Value()
}

func (r RatingOption) DisplayOrder() int {
	return r.displayOrder
}

// Business logic methods

// IsApplicableToRole checks if this rating option applies to the given role
func (r RatingOption) IsApplicableToRole(role string) bool {
	entityRole, err := core.NewEntityRole(role)
	if err != nil {
		return false
	}

	for _, applicableRole := range r.applicableToRoles {
		if applicableRole.Value() == entityRole.Value() {
			return true
		}
	}
	return false
}

// IsApplicableToStarRating checks if this rating option  applies to the given star rating
func (r RatingOption) IsApplicableToStarRating(stars int) bool {
	if r.minStars == nil {
		return true
	}
	return stars >= int(r.minStars.Value())
}

// IsApplicableToFieldTag checks if this rating option applies to the given field tag
func (r RatingOption) IsApplicableToFieldTag(fieldTag *string) bool {
	if r.fieldTag.IsNil() {
		return true // No field tag restriction means applies to all fields
	}

	if fieldTag == nil {
		return r.fieldTag.IsNil()
	}

	return r.fieldTag.String() == *fieldTag
}

// Matches checks if this rating option matches all the given criteria
func (r RatingOption) Matches(role string, stars int, fieldTag *string) bool {
	return r.IsApplicableToRole(role) &&
		r.IsApplicableToStarRating(stars) &&
		r.IsApplicableToFieldTag(fieldTag)
}
