package repository

import (
	"testing"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
)

func TestRatingOptionModel_StructValidation(t *testing.T) {
	tests := []struct {
		name string
		dto  RatingOptionModel
	}{
		{
			name: "valid DTO with all fields",
			dto: RatingOptionModel{
				ID:                1,
				OptionType:        "overall_experience",
				Value:             "Excellent service",
				ApplicableToRoles: pq.StringArray{"user", "pro"},
				MinStars:          intPtr(4),
				FieldTag:          stringPtr("service"),
				DisplayOrder:      1,
			},
		},
		{
			name: "valid DTO with minimal fields",
			dto: RatingOptionModel{
				ID:                2,
				OptionType:        "professional_skills",
				Value:             "Great technical skills",
				ApplicableToRoles: pq.StringArray{"pro"},
				MinStars:          nil,
				FieldTag:          nil,
				DisplayOrder:      2,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the DTO can be created and accessed properly
			assert.Equal(t, tt.dto.ID, tt.dto.ID)
			assert.Equal(t, tt.dto.OptionType, tt.dto.OptionType)
			assert.Equal(t, tt.dto.Value, tt.dto.Value)
			assert.Equal(t, tt.dto.ApplicableToRoles, tt.dto.ApplicableToRoles)
			assert.Equal(t, tt.dto.MinStars, tt.dto.MinStars)
			assert.Equal(t, tt.dto.FieldTag, tt.dto.FieldTag)
			assert.Equal(t, tt.dto.DisplayOrder, tt.dto.DisplayOrder)
		})
	}
}

func TestGetRatingOptionsFilter_Validation(t *testing.T) {
	tests := []struct {
		name   string
		filter GetRatingOptionsFilter
	}{
		{
			name: "filter with all fields",
			filter: GetRatingOptionsFilter{
				OptionType:        stringPtr("overall_experience"),
				ApplicableToRoles: []string{"user", "pro"},
				MinStars:          4,
				FieldTag:          stringPtr("service"),
			},
		},
		{
			name: "filter with minimal fields",
			filter: GetRatingOptionsFilter{
				ApplicableToRoles: []string{"user"},
				MinStars:          1,
			},
		},
		{
			name: "filter with empty roles",
			filter: GetRatingOptionsFilter{
				ApplicableToRoles: []string{},
				MinStars:          3,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the filter can be created and accessed properly
			assert.Equal(t, tt.filter.OptionType, tt.filter.OptionType)
			assert.Equal(t, tt.filter.ApplicableToRoles, tt.filter.ApplicableToRoles)
			assert.Equal(t, tt.filter.MinStars, tt.filter.MinStars)
			assert.Equal(t, tt.filter.FieldTag, tt.filter.FieldTag)
		})
	}
}

func TestGetRatingOptionsResult_Validation(t *testing.T) {
	t.Run("result with multiple options", func(t *testing.T) {
		result := GetRatingOptionsResult{
			Options: []RatingOptionModel{
				{
					ID:                1,
					OptionType:        "overall_experience",
					Value:             "Excellent service",
					ApplicableToRoles: pq.StringArray{"user", "pro"},
					MinStars:          intPtr(4),
					FieldTag:          stringPtr("service"),
					DisplayOrder:      1,
				},
				{
					ID:                2,
					OptionType:        "professional_skills",
					Value:             "Great technical skills",
					ApplicableToRoles: pq.StringArray{"pro"},
					MinStars:          nil,
					FieldTag:          nil,
					DisplayOrder:      2,
				},
			},
		}

		assert.Len(t, result.Options, 2)
		assert.Equal(t, 1, result.Options[0].ID)
		assert.Equal(t, "overall_experience", result.Options[0].OptionType)
		assert.Equal(t, 2, result.Options[1].ID)
		assert.Equal(t, "professional_skills", result.Options[1].OptionType)
	})

	t.Run("result with empty options", func(t *testing.T) {
		result := GetRatingOptionsResult{
			Options: []RatingOptionModel{},
		}

		assert.Len(t, result.Options, 0)
		assert.NotNil(t, result.Options)
	})
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func stringPtr(s string) *string {
	return &s
}
