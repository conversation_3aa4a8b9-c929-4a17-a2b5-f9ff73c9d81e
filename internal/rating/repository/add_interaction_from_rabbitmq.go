package repository

import (
	"context"
	"database/sql"
	"encoding/json"

	"time"
)

type Interaction struct {
	InteractionID      string                 `db:"interaction_id" json:"interaction_id"`
	EventID            string                 `db:"event_id" json:"event_id"`
	FromRole           string                 `db:"from_role" json:"from_role"`
	FromEntityID       string                 `db:"from_entity_id" json:"from_entity_id"`
	ToRole             string                 `db:"to_role" json:"to_role"`
	ToEntityID         string                 `db:"to_entity_id" json:"to_entity_id"`
	FieldTag           string                 `db:"field_tag" json:"field_tag"`
	StarRating         int                    `db:"star_rating" json:"star_rating"`
	OverallExperience  string                 `db:"overall_experience" json:"overall_experience"`
	ProfessionalSkills string                 `db:"professional_skills" json:"professional_skills"`
	PersonalSkills     string                 `db:"personal_skills" json:"personal_skills"`
	ThankingNote       string                 `db:"thanking_note" json:"thanking_note"`
	FormData           map[string]interface{} `db:"form_data" json:"form_data"`
	SubmittedAt        time.Time              `db:"submitted_at" json:"submitted_at"`
	CorrelationID      string                 `db:"correlation_id" json:"correlation_id"`
	EngagementSkills   string                 `db:"engagement_skills" json:"engagement_skills"`
}

func InsertInteractionWithLogBook(ctx context.Context, db *sql.DB, interaction *Interaction) error {
	// Begin a transaction
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 1. Insert into interactions table
	formData, err := json.Marshal(interaction.FormData)
	if err != nil {
		return err
	}

	_, err = tx.ExecContext(ctx, `
		INSERT INTO interactions (
			interaction_id, event_id, from_role, from_entity_id, to_role, to_entity_id, field_tag, star_rating, 
			overall_experience, professional_skills, personal_skills, thanking_note, form_data, submitted_at, correlation_id, engagement_skills
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
		)
	`,
		interaction.InteractionID,
		interaction.EventID,
		interaction.FromRole,
		interaction.FromEntityID,
		interaction.ToRole,
		interaction.ToEntityID,
		interaction.FieldTag,
		interaction.StarRating,
		interaction.OverallExperience,
		interaction.ProfessionalSkills,
		interaction.PersonalSkills,
		interaction.ThankingNote,
		formData,
		interaction.SubmittedAt,
		interaction.CorrelationID,
		interaction.EngagementSkills,
	)
	if err != nil {
		return err
	}

	// 2. Insert into log_books table
	// Marshal the entire interaction as JSON for event_data
	eventData, err := json.Marshal(interaction)
	if err != nil {
		return err
	}

	var logBookID string
	err = tx.QueryRowContext(ctx, `
		INSERT INTO log_books (
			entity_id, entity_role, event_data, correlation_id
		) VALUES (
			$1, $2, $3, $4
		) RETURNING id
	`,
		interaction.FromEntityID,
		interaction.FromRole,
		eventData,
		interaction.CorrelationID,
	).Scan(&logBookID)
	if err != nil {
		return err
	}

	// 3. Insert into log_book_interactions table
	_, err = tx.ExecContext(ctx, `
		INSERT INTO log_book_interactions (
			log_book_id, interaction_id
		) VALUES (
			$1, $2
		)
	`,
		logBookID,
		interaction.InteractionID,
	)
	if err != nil {
		return err
	}

	// Commit the transaction
	return tx.Commit()
}
