package transport

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/country/usecase"
)

type searchCountriesUsecase interface {
	Execute(ctx context.Context, input *usecase.SearchCountriesInput) (*usecase.SearchCountriesOutput, error)
}

type SearchCountriesHandler struct {
	usecase searchCountriesUsecase
}

func NewSearchCountriesHandler(usecase searchCountriesUsecase) *SearchCountriesHandler {
	return &SearchCountriesHandler{
		usecase: usecase,
	}
}

func (h *SearchCountriesHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input SearchCountriesInput
		if err := c.Bind(&input); err != nil {
			return err
		}

		countries, err := h.usecase.Execute(ctx, &usecase.SearchCountriesInput{
			Enabled: input.Enabled,
		})
		if err != nil {
			return err
		}

		out := parseSearchCountriesResponse(countries)

		return c.<PERSON>(200, out)
	}
}

type SearchCountriesInput struct {
	Enabled *bool `query:"enabled"`
}

type SearchCountriesModel struct {
	ID              string    `json:"id"`
	Name            string    `json:"name"`
	ISOCode         string    `json:"iso_code,omitempty"`
	CountryCode     string    `json:"country_code,omitempty"`
	Icon            string    `json:"icon"`
	Continent       int       `json:"continent,omitempty"`
	Latitude        float64   `json:"latitude,omitempty"`
	Longitude       float64   `json:"longitude,omitempty"`
	Enabled         bool      `json:"enabled"`
	PhoneNumberMask *string   `json:"phone_number_mask,omitempty"`
	CreatedAt       time.Time `json:"created_at,omitempty"`
	UpdatedAt       time.Time `json:"updated_at,omitempty"`
}

type SearchCountriesOutput struct {
	Data []*SearchCountriesModel `json:"data"`
}

func parseSearchCountriesResponse(input *usecase.SearchCountriesOutput) *SearchCountriesOutput {
	var countries []*SearchCountriesModel
	for _, country := range input.Data {
		countries = append(countries, &SearchCountriesModel{
			ID:              country.ID,
			Name:            country.Name,
			ISOCode:         country.ISOCode,
			CountryCode:     country.CountryCode,
			Icon:            country.Icon,
			Continent:       country.Continent,
			Latitude:        country.Latitude,
			Longitude:       country.Longitude,
			Enabled:         country.Enabled,
			PhoneNumberMask: country.PhoneNumberMask,
			CreatedAt:       country.CreatedAt,
			UpdatedAt:       country.UpdatedAt,
		})
	}
	return &SearchCountriesOutput{Data: countries}
}
