package repository

import (
	"context"
	"fmt"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"time"
)

func (c *countryRepo) SearchCountries(ctx context.Context, input *SearchCountryFilter) (*SearchCountriesResponse, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "searchCountries"), vlog.F("action", "search all countries with filter"))
	query := `SELECT id, name, iso_code, country_code, icon, continent, latitude, longitude, enabled, created_at, updated_at, phone_number_mask FROM country WHERE 1 = 1`

	var args = make([]interface{}, 0)
	if input != nil {
		if input.Enabled != nil {
			query += fmt.Sprintf(" AND enabled = $%d", len(args)+1)
			args = append(args, *input.Enabled)
		}
	}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	rows, err := c.db.QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	defer rows.Close()

	var countries []SearchCountriesModel
	for rows.Next() {
		var country SearchCountriesModel
		if err := rows.StructScan(&country); err != nil {
			return nil, err
		}
		countries = append(countries, country)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}
	return &SearchCountriesResponse{Countries: countries}, nil
}

type SearchCountryFilter struct {
	Enabled *bool
}

type SearchCountriesModel struct {
	ID              string    `db:"id"`
	Name            string    `db:"name"`
	ISOCode         string    `db:"iso_code"`
	CountryCode     string    `db:"country_code"`
	Icon            string    `db:"icon"`
	Continent       int       `db:"continent"`
	Latitude        float64   `db:"latitude"`
	Longitude       float64   `db:"longitude"`
	Enabled         bool      `db:"enabled"`
	PhoneNumberMask *string   `db:"phone_number_mask"`
	CreatedAt       time.Time `db:"created_at"`
	UpdatedAt       time.Time `db:"updated_at"`
}
type SearchCountriesResponse struct {
	Countries []SearchCountriesModel `json:"countries"`
}
