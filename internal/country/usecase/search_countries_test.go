package usecase_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"gitlab.viswalslab.com/backend/standard/v2/vlog"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/country/repository"
	uc "gitlab.viswalslab.com/backend/ratings/internal/country/usecase"
	mockUC "gitlab.viswalslab.com/backend/ratings/mocks/internal_mock/country/usecase"
	"go.uber.org/mock/gomock"
)

func TestGetAllCountriesUsecase_Execute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockRepo := mockUC.NewMocksearchCountriesRepo(ctrl)
	u := uc.NewSearchCountriesUsecase(mockRepo)

	t.Run("success", func(t *testing.T) {
		mockResponse := &repository.SearchCountriesResponse{
			Countries: []repository.SearchCountriesModel{
				{
					ID:          "1",
					Name:        "Country1",
					ISOCode:     "ISO1",
					CountryCode: "+1",
					Icon:        "icon1.png",
					Continent:   1,
					Latitude:    12.34,
					Longitude:   56.78,
					Enabled:     true,
					CreatedAt:   time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC),
					UpdatedAt:   time.Date(2020, 20, 20, 20, 20, 20, 20, time.UTC),
				},
			},
		}

		mockRepo.EXPECT().SearchCountries(gomock.Any(), gomock.Any()).Return(mockResponse, nil)

		output, err := u.Execute(ctx, nil)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.Len(t, output.Data, 1)
		assert.Equal(t, "Country1", output.Data[0].Name)
	})

	t.Run("error", func(t *testing.T) {
		mockRepo.EXPECT().SearchCountries(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))

		output, err := u.Execute(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, output)
	})
}
