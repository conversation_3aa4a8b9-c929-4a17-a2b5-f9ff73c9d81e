package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/ratings/core"
)

type Country struct {
	id              core.Identifier
	name            string
	isoCode         string
	countryCode     string
	icon            string
	continent       int
	latitude        core.Latitude
	longitude       core.Longitude
	enabled         bool
	phoneNumberMask *string
	createdAt       core.Timestamp
	updatedAt       core.Timestamp
}

type NewCountryInput struct {
	ID              string
	Name            string
	ISOCode         string
	CountryCode     string
	Icon            string
	Continent       int
	Latitude        float64
	Longitude       float64
	Enabled         bool
	PhoneNumberMask *string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func NewCountry(input *NewCountryInput) (*Country, error) {
	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, err
	}

	lat, err := core.NewLatitude(input.Latitude)
	if err != nil {
		return nil, err
	}

	lon, err := core.NewLongitude(input.Longitude)
	if err != nil {
		return nil, err
	}

	createdAt, err := core.NewTimestampFromTime(input.CreatedAt)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(input.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &Country{
		id:              *id,
		name:            input.Name,
		isoCode:         input.ISOCode,
		countryCode:     input.CountryCode,
		icon:            input.Icon,
		continent:       input.Continent,
		latitude:        *lat,
		longitude:       *lon,
		enabled:         input.Enabled,
		phoneNumberMask: input.PhoneNumberMask,
		createdAt:       *createdAt,
		updatedAt:       *updatedAt,
	}, nil
}

type HydrateCountry struct {
	ID              string
	Name            string
	ISOCode         string
	CountryCode     string
	Icon            string
	Continent       int
	Latitude        float64
	Longitude       float64
	Enabled         bool
	PhoneNumberMask *string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (c Country) ID() string               { return c.id.Value() }
func (c Country) Name() string             { return c.name }
func (c Country) ISOCode() string          { return c.isoCode }
func (c Country) CountryCode() string      { return c.countryCode }
func (c Country) Icon() string             { return c.icon }
func (c Country) Continent() int           { return c.continent }
func (c Country) Latitude() float64        { return c.latitude.Value() }
func (c Country) Longitude() float64       { return c.longitude.Value() }
func (c Country) Enabled() bool            { return c.enabled }
func (c Country) PhoneNumberMask() *string { return c.phoneNumberMask }
func (c Country) CreatedAt() time.Time     { return c.createdAt.Value() }
func (c Country) UpdatedAt() time.Time     { return c.updatedAt.Value() }
