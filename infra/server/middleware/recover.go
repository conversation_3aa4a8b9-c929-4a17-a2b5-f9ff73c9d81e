package middleware

import (
	"fmt"
	"net/http"
	"runtime"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func Recover() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			defer func() {
				ctx := c.Request().Context()
				logger := vlog.FromContext(ctx)

				if r := recover(); r != nil {
					err, ok := r.(error)
					if !ok {
						err = fmt.Errorf("%v", r)
					}

					stack := make([]byte, 4<<10) // 4 KB
					length := runtime.Stack(stack, false)

					logger.Error("PANIC RECOVERED",
						vlog.F("error", err),
						vlog.F("stack", string(stack[:length])),
						vlog.F("url", c.Request().URL.String()),
						vlog.F("method", c.Request().Method))

					c.Error(echo.NewHTTPError(http.StatusInternalServerError, "Internal server error"))
				}
			}()
			return next(c)
		}
	}
}
