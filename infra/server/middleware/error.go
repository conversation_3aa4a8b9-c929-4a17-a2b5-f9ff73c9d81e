package middleware

import (
	"errors"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type ErrorResponse struct {
	Code    string            `json:"code,omitempty"`
	Error   string            `json:"error"`
	Message string            `json:"message"`
	Fields  map[string]string `json:"fields,omitempty"`
}

func Error() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			var (
				err    = next(c)
				ctx    = c.Request().Context()
				logger = vlog.FromContext(ctx)
			)

			// no errors
			if err == nil {
				return nil
			}

			// http client errors
			var validationErrs validator.ValidationErrors
			if errors.As(err, &validationErrs) {
				logger.Debug("validation failed", vlog.F("error", err))
				errs := make(map[string]string)
				for _, vErr := range validationErrs {
					errs[vErr.StructField()] = vErr.Tag()
				}

				return c.JSON(http.StatusBadRequest, ErrorResponse{
					Error:  "validation failed",
					Fields: errs,
				})
			}

			// business errors
			var businessErr *core.BusinessError
			if errors.As(err, &businessErr) {
				return c.JSON(http.StatusConflict, ErrorResponse{
					Error:   "business conflict",
					Message: err.Error(),
				})
			}

			// item not found
			if errors.Is(err, core.ErrNoItemFound) {
				return c.String(http.StatusNotFound, err.Error())
			}

			if errors.Is(err, core.ErrForbidden) {
				return c.JSON(http.StatusForbidden, ErrorResponse{
					Error:   "forbidden",
					Message: err.Error(),
				})
			}

			var e *echo.HTTPError
			if errors.As(err, &e) {
				return c.JSON(e.Code, e.Message)
			}

			logger.Debug("internal server error", vlog.F("error", err))

			// internal server error
			return c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "internal server error",
				Message: "an unexpected error occurred",
			})
		}
	}
}
