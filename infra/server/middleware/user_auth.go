package middleware

import (
	"context"
	"errors"
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/pkg/cognito"
)

type userAuthProvider interface {
	IsUserValid(ctx context.Context, userID string, userPoolID string) error
}

type UserAuth struct {
	clientID   string
	userPoolID string
	provider   userAuthProvider
}

func NewUserAuth(provider userAuthProvider, clientID, userPoolID string) *UserAuth {
	return &UserAuth{
		clientID:   clientID,
		userPoolID: userPoolID,
		provider:   provider,
	}
}

func (a *UserAuth) UserAuthMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			ctx := c.Request().Context()
			userID := c.Get("userID").(string)

			err := a.provider.IsUserValid(ctx, userID, a.userPoolID)
			if err != nil {
				if errors.Is(err, cognito.ErrInvalidUser) {
					return c.JSON(http.StatusForbidden, ErrorResponse{
						Error:   "forbidden",
						Message: err.Error(),
					})
				}

				return c.JSON(http.StatusInternalServerError, ErrorResponse{
					Error:   "internal server error",
					Message: "failed to check if user is valid",
				})
			}

			return next(c)
		}
	}
}
