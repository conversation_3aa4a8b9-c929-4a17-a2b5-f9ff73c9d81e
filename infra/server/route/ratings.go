package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
)

type ratingFactory interface {
	GetRatingOptionsHandler() *transport.GetRatingOptionsHandler
}

type ratingRouter struct {
	baseRoute *echo.Group
	factory   ratingFactory
}

func NewRatingRouter(e *echo.Group, factory ratingFactory) *ratingRouter {
	return &ratingRouter{
		baseRoute: e,
		factory:   factory,
	}
}

func (r *ratingRouter) Route() {
	getRatingOptionsHandler := r.factory.GetRatingOptionsHandler()

	group := r.baseRoute.Group("/ratings")
	group.POST("/options", getRatingOptionsHandler.Handle())
}
