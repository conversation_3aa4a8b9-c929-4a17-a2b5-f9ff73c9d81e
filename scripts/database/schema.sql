-- create the database
CREATE DATABASE rating
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;


-- Create user: rating_admin
CREATE ROLE rating_admin WITH LOGIN PASSWORD 'password here';

-- Give access to the user
GRANT CONNECT ON DATABASE rating TO rating_admin;

-- Grant <PERSON>GE on the public schema (or whatever schema you use)
GRANT USAGE ON SCHEMA public TO rating_admin;

-- Grant privileges on all existing tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO rating_admin;

-- Also make sure future tables are covered
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO rating_admin;

