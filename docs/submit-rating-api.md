# Submit Rating API

This document describes the new Submit Rating API endpoint that allows submitting rating data to RabbitMQ for asynchronous processing.

## Endpoint

**POST** `/rating/v1/ratings/submit`

## Description

This endpoint accepts rating submission data and publishes it to a RabbitMQ queue for asynchronous processing. The subscriber (already implemented) will consume the message and store the rating data in the database.

## Request Body

```json
{
  "interaction_id": "string (required)",
  "event_id": "string (required)",
  "from_role": "string (required)",
  "from_entity_id": "string (required)",
  "to_role": "string (required)",
  "to_entity_id": "string (required)",
  "field_tag": "string (required)",
  "star_rating": "integer (required, 1-5)",
  "overall_experience": "string (optional)",
  "professional_skills": "string (optional)",
  "personal_skills": "string (optional)",
  "thanking_note": "string (optional)",
  "engagement_skills": "string (optional)",
  "form_data": "object (optional)"
}
```

### Field Descriptions

- **interaction_id**: Unique identifier for the interaction
- **event_id**: Identifier for the event this rating relates to
- **from_role**: Role of the entity giving the rating (e.g., "pro", "team_member")
- **from_entity_id**: ID of the entity giving the rating
- **to_role**: Role of the entity being rated
- **to_entity_id**: ID of the entity being rated
- **field_tag**: Tag identifying the field being rated (e.g., "overall_experience")
- **star_rating**: Rating value from 1 to 5 stars
- **overall_experience**: Optional text describing overall experience
- **professional_skills**: Optional text about professional skills
- **personal_skills**: Optional text about personal skills
- **thanking_note**: Optional thank you note
- **engagement_skills**: Optional text about engagement skills
- **form_data**: Optional object containing additional form data

## Response

### Success Response (200 OK)

```json
{
  "success": true,
  "correlation_id": "uuid-string",
  "message": "Rating submitted successfully"
}
```

### Error Responses

#### 400 Bad Request - Missing Required Fields

```json
{
  "error": "interaction_id is required"
}
```

#### 400 Bad Request - Invalid Star Rating

```json
{
  "error": "star_rating must be between 1 and 5"
}
```

#### 500 Internal Server Error

```json
{
  "error": "internal server error",
  "message": "an unexpected error occurred"
}
```

## Example Usage

### cURL Example

```bash
curl -X POST http://localhost:8080/rating/v1/ratings/submit \
  -H "Content-Type: application/json" \
  -d '{
    "interaction_id": "interaction-123",
    "event_id": "event-456",
    "from_role": "pro",
    "from_entity_id": "pro-789",
    "to_role": "team_member",
    "to_entity_id": "member-101",
    "field_tag": "overall_experience",
    "star_rating": 5,
    "overall_experience": "Excellent collaboration",
    "professional_skills": "Very skilled and knowledgeable",
    "personal_skills": "Great communication",
    "thanking_note": "Thank you for the great work!",
    "engagement_skills": "Highly engaged throughout the project",
    "form_data": {
      "project_type": "web_development",
      "duration_weeks": 4
    }
  }'
```

### JavaScript Example

```javascript
const response = await fetch('/rating/v1/ratings/submit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    interaction_id: 'interaction-123',
    event_id: 'event-456',
    from_role: 'pro',
    from_entity_id: 'pro-789',
    to_role: 'team_member',
    to_entity_id: 'member-101',
    field_tag: 'overall_experience',
    star_rating: 5,
    overall_experience: 'Excellent collaboration',
    professional_skills: 'Very skilled and knowledgeable',
    personal_skills: 'Great communication',
    thanking_note: 'Thank you for the great work!',
    engagement_skills: 'Highly engaged throughout the project',
    form_data: {
      project_type: 'web_development',
      duration_weeks: 4
    }
  })
});

const result = await response.json();
console.log('Rating submitted:', result);
```

## Architecture

1. **API Endpoint**: Receives the rating submission request
2. **Validation**: Validates required fields and data types
3. **RabbitMQ Publishing**: Publishes the rating data to the configured queue
4. **Asynchronous Processing**: The existing subscriber processes the message and stores it in the database
5. **Response**: Returns success confirmation with correlation ID

## Configuration

The API uses the `MESSAGING_RATING_QUEUE_NAME` environment variable to determine which RabbitMQ queue to publish to. This should match the queue name configured for the subscriber.

## Error Handling

- Input validation errors return 400 Bad Request
- RabbitMQ publishing errors return 500 Internal Server Error
- All errors are logged with correlation IDs for debugging

## Testing

The implementation includes comprehensive unit tests for both the transport handler and the usecase layer. Run tests with:

```bash
go test ./internal/rating/transport -v
go test ./internal/rating/usecase -v
```
