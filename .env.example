# Application Settings
APP_NAME=ratings
ENVIRONMENT=local
LOG_LEVEL=debug
HOST=localhost
PORT=:8080

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/rating?sslmode=disable

# Congnito Configuration
# Access keys
AWS_COGNITO_IAM_R4B_ACCESS_KEY='your access key'
AWS_COGNITO_IAM_R4B_SECRET_KEY='your secret key'

# User
AWS_COGNITO_PATIENT_USER_POOL_ID='user pool id'
AWS_COGNITO_PATIENT_USER_REGION=eu-north-1
AWS_COGNITO_R4B_IGNITE_APP_CLIENT_ID='user client id'

# Messaging Configuration
BROKER_URL=amqp://user:password@localhost:5672/

# Rating Queue Configuration
MESSAGING_RATING_QUEUE_NAME=ratings

# env variable for server

RABBITMQ_RETRY_INITIAL_INTERVAL=5s
RABBITMQ_RETRY_MULTIPLIER=2.0
RABBITMQ_RETRY_MAX_INTERVAL=30m

RABBITMQ_USER=user
RABBITMQ_PASSWORD=password
RABBITMQ_HOST=localhost
RABBITMQ_PORT=15673

CACHE_URL=redis://localhost:6379
CACHE_PASSWORD=password
