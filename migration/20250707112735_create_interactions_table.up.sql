-- Create interactions table
CREATE TABLE interactions (
    interaction_id VARCHAR(36) PRIMARY KEY,
    event_id VARCHAR(36) NOT NULL,
    from_role VARCHAR(50) NOT NULL,
    from_entity_id VARCHAR(36) NOT NULL,
    to_role VARCHAR(50) NOT NULL,
    to_entity_id VARCHAR(36) NOT NULL,
    field_tag VARCHAR(50),
    star_rating INT NOT NULL,
    overall_experience TEXT,
    engagement_skills TEXT,
    professional_skills TEXT,
    personal_skills TEXT,
    thanking_note TEXT,
    form_data JSONB,
    submitted_at TIMESTAMPTZ NOT NULL,
    correlation_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for interactions table
CREATE INDEX idx_interactions_event_id ON interactions(event_id);
CREATE INDEX idx_interactions_from_entity ON interactions(from_role, from_entity_id);
CREATE INDEX idx_interactions_to_entity ON interactions(to_role, to_entity_id);
CREATE INDEX idx_interactions_submitted_at ON interactions(submitted_at);