-- Create log_book_interactions table
CREATE TABLE log_book_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_book_id UUID NOT NULL,
    interaction_id UUID NOT NULL,
    FOREIGN KEY (log_book_id) REFERENCES log_books(id)
);

-- Create index on log_book_interactions
CREATE INDEX idx_lbi_log_book ON log_book_interactions(log_book_id);
CREATE INDEX idx_lbi_interaction ON log_book_interactions(interaction_id);