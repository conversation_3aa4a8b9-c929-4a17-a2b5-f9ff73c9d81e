-- Create log_books table
CREATE TABLE log_books (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL,
    entity_role VARCHAR(20) NOT NULL,
    event_data JSONB,
    correlation_id UUID,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for log_books table
CREATE INDEX idx_log_books_entity ON log_books(entity_id, entity_role);
CREATE INDEX idx_log_books_correlation_id ON log_books(correlation_id);