package main

import (
	"context"
	"os"

	"gitlab.viswalslab.com/backend/ratings/pkg/cognito"

	"github.com/labstack/echo/v4"
	echoMiddleware "github.com/labstack/echo/v4/middleware"
	"gitlab.viswalslab.com/backend/standard/v2/correlationid"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"

	"gitlab.viswalslab.com/backend/ratings/infra/config"
	"gitlab.viswalslab.com/backend/ratings/infra/health"
	"gitlab.viswalslab.com/backend/ratings/infra/server"
	"gitlab.viswalslab.com/backend/ratings/infra/server/middleware"
	"gitlab.viswalslab.com/backend/ratings/pkg/database"
	"gitlab.viswalslab.com/backend/ratings/pkg/rabbitmq"
)

func main() {
	err := run()
	if err != nil {
		os.Exit(1)
	}

	os.Exit(0)
}

func run() (err error) {
	var (
		ctx = context.Background()
		cfg = config.FromEnv()
	)

	logger := vlog.NewWithLevel(cfg.LogLevel)
	defer logger.Flush()

	logger = logger.With(
		vlog.F("application", cfg.AppName),
		vlog.F("environment", cfg.Environment))

	logger.Info("initializing")

	logger.Info("create account through queue status", vlog.F("status", cfg.IsCreateAccountByQueueEnabled))

	ctx = vlog.AttachLoggerToContext(ctx, logger)

	conn, err := database.New(database.Postgres, cfg.DataBase.URL)
	if err != nil {
		logger.Error("failed to create database instance", vlog.F("error", err))
		return err
	}

	db, err := conn.Open(ctx)
	if err != nil {
		logger.Error("failed to open database connection", vlog.F("error", err))
		return err
	}
	defer db.Close()

	messaging, err := rabbitmq.NewRabbitMQ(ctx, cfg.Messaging)
	if err != nil {
		logger.Error("failed to connect to rabbitMQ instance", vlog.F("error", err))
		return err
	}

	messaging.Retrier = messaging.Retrier.WithInitialInterval(cfg.Messaging.RetryConfig.InitialInterval).WithMultiplier(cfg.Messaging.RetryConfig.Multiplier).WithMaxInterval(cfg.Messaging.RetryConfig.MaxInterval)

	messageCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	router := bootstrapRouter(logger)
	baseGroup := router.Group("/rating")
	mainGroup := baseGroup.Group("/v1")

	healthHandler := health.NewHealthChecker(db)
	baseGroup.GET("/health", healthHandler.Handle())

	cg, err := cognito.NewCognito(ctx, cfg.UserAuth.DefaultRegion, cfg.UserAuth.AccessKeyID, cfg.UserAuth.SecretAccessKey)
	if err != nil {
		logger.Error("failed to create cognito client", vlog.F("error", err))
		return err
	}

	app, err := server.NewBuilder().
		WithContext(ctx).
		WithMessagingContext(messageCtx).
		WithCancelMessagingContext(cancel).
		WithConfiguration(cfg).
		WithDatabase(db).
		WithRouter(router).
		WithBaseGroup(mainGroup).
		WithLogger(logger).
		WithAuthProvider(cg).
		WithMessaging(messaging).
		Build()

	if err != nil {
		logger.Error("failed to create server instance", vlog.F("error", err))
		return err
	}

	app.Run()

	return nil
}

func bootstrapRouter(logger vlog.Logger) *echo.Echo {

	router := echo.New()
	router.Pre(echoMiddleware.RemoveTrailingSlash())
	router.Use(middleware.Recover())
	router.Use(correlationid.EchoMiddleware(logger))
	router.Use(middleware.Error())

	return router
}
