package redis

import (
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
)

type Config struct {
	URL      string
	Password string
}

type Provider struct {
	client *redis.Client
}

func NewProvider(ctx context.Context, config *Config) (*Provider, error) {
	opt, err := redis.ParseURL(config.URL)
	if err != nil {
		return nil, err
	}

	client := redis.NewClient(opt)

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	return &Provider{client: client}, nil
}

func (p *Provider) Ping(ctx context.Context) error {
	out, err := p.client.Ping(ctx).Result()
	if err != nil {
		return err
	}

	if out != "PONG" {
		return errors.New("unexpected response from Redis: " + out)
	}

	return nil
}
