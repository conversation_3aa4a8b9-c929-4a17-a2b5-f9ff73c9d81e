package cognito

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	awsCognito "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
)

type Cognito struct {
	Client *awsCognito.Client
}

func NewCognito(ctx context.Context, region, accessKey, secretKey string) (*Cognito, error) {
	// Create custom config with static credentials
	cfg, err := config.LoadDefaultConfig(
		ctx,
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
	)
	if err != nil {
		return nil, err
	}

	client := awsCognito.NewFromConfig(cfg)

	return &Cognito{
		Client: client,
	}, nil
}
