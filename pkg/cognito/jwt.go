package cognito

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"

	"github.com/golang-jwt/jwt"

	"github.com/labstack/echo/v4"
)

type JWKS struct {
	Keys []json.RawMessage `json:"keys"`
}

type Public<PERSON>ey struct {
	Kid string `json:"kid"`
	Kty string `json:"kty"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

func (c *Cognito) ParseJWT(userPoolID, Region string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			header := c.Request().Header.Get("Authorization")
			if header == "" {
				return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Unauthorized no authorization header"})
			}

			cleanToken := removeBearerPrefix(header)

			isAllowed, userID, err := verifyToken(cleanToken, userPoolID, Region)
			if userID != nil {
				c.Set("userID", *userID)
			}

			if err != nil {
				return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Unauthorized invalid token"})
			}

			if !isAllowed {
				return c.JSON(http.StatusForbidden, map[string]string{"error": "Forbidden invalid user"})
			}

			return next(c)
		}
	}
}

func getJWKS(userPoolID, region string) (*JWKS, error) {
	url := fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/%s/.well-known/jwks.json", region, userPoolID)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch JWKS: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch JWKS, status: %v", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read JWKS response body: %v", err)
	}

	var jwks JWKS
	if err := json.Unmarshal(body, &jwks); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JWKS: %v", err)
	}

	return &jwks, nil
}

func getRSAPublicKey(n, e string) (*rsa.PublicKey, error) {
	modulus, err := jwt.DecodeSegment(n)
	if err != nil {
		return nil, err
	}

	exponent, err := jwt.DecodeSegment(e)
	if err != nil {
		return nil, err
	}

	pubKey := &rsa.PublicKey{
		N: new(big.Int).SetBytes(modulus),
		E: int(new(big.Int).SetBytes(exponent).Int64()),
	}
	return pubKey, nil
}

func verifyToken(tokenString, userPoolID, region string) (bool, *string, error) {
	jwks, err := getJWKS(userPoolID, region)
	if err != nil {
		return false, nil, err
	}

	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		kid := token.Header["kid"].(string)

		for _, rawKey := range jwks.Keys {
			var pubKey PublicKey
			if err := json.Unmarshal(rawKey, &pubKey); err != nil {
				continue
			}

			if pubKey.Kid == kid {
				return getRSAPublicKey(pubKey.N, pubKey.E)
			}
		}
		return nil, fmt.Errorf("unable to find the appropriate key")
	})
	if err != nil {
		return false, nil, fmt.Errorf("failed to parse token")
	}

	if !token.Valid {
		return false, nil, fmt.Errorf("invalid token")
	}

	userID, err := extractUserName(token)
	if err != nil {
		return false, nil, err
	}

	return userID != nil, userID, nil
}

func extractUserName(token *jwt.Token) (*string, error) {
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	username, found := claims["username"]
	if !found {
		return nil, nil
	}

	usernameStr, ok := username.(string)
	if !ok {
		return nil, fmt.Errorf("username claim is not a string")
	}

	return &usernameStr, nil
}

func removeBearerPrefix(token string) string {
	const bearerPrefix = "Bearer "
	if len(token) > len(bearerPrefix) && token[:len(bearerPrefix)] == bearerPrefix {
		return token[len(bearerPrefix):]
	}
	return token
}
