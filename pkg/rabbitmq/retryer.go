package rabbitmq

import (
	"context"
	"errors"
	"fmt"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// TODO: push actual backoff value to gitlab
const (
	DefaultInitialInterval = 2 * time.Second
	DefaultMultiplier      = 1.0
	DefaultMaxInterval     = 3 * time.Minute
	//MaxElapsedTime = 30*time.Minute
)

const (
	DebugConnection = "creating rabbitmq connection"
	DebugChannel    = "creating new rabbitmq channel"
	DebugQueue      = "creating to a rabbitmq queue"
	DebugPublish    = "publishing message to a rabbitmq"
)

type Retrier struct {
	initialInterval time.Duration
	// currently trying indefinitely
	//MaxElapsedTime time.Duration
	multiplier  float64
	maxInterval time.Duration
}

func NewRetrier(config config.RetryConfig) (*Retrier, error) {
	return &Retrier{
		initialInterval: config.InitialInterval,
		multiplier:      config.Multiplier,
		maxInterval:     config.MaxInterval,
	}, nil
}

// WithInitialInterval sets the initial interval for the retrier
func (r *Retrier) WithInitialInterval(t time.Duration) *Retrier {
	r.initialInterval = t
	return r
}

// WithMultiplier sets the multiplier for the retrier
func (r *Retrier) WithMultiplier(f float64) *Retrier {
	r.multiplier = f
	return r
}

// WithMaxInterval sets the max interval for the retrier
func (r *Retrier) WithMaxInterval(t time.Duration) *Retrier {
	r.maxInterval = t
	return r
}

func (r *Retrier) retry(ctx context.Context, action string, returnOnClose bool, operation func() error) error {
	logger := vlog.FromContext(ctx).With(vlog.F("retrying action", action))
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			err := operation()
			if err != nil {
				logger.Warn("Failed to perform the action: ", vlog.F("error", err))

				if errors.Is(err, ErrNotInitialized) {
					return err
				}

				if returnOnClose && errors.Is(err, amqp.ErrClosed) {
					return err
				}

				logger.Warn("retrying in ", vlog.F("time", r.initialInterval.String()))

				time.Sleep(r.initialInterval)

				r.initialInterval = time.Duration(float64(r.initialInterval) * r.multiplier)
				if r.initialInterval > r.maxInterval {
					r.initialInterval = r.maxInterval
				}
			} else {
				return nil
			}
		}
	}
}

// CreateConnection implements retry logic for creating connection with rabbitmq
func (r *Retrier) CreateConnection(ctx context.Context, url string, cfg *amqp.Config) (*amqp.Connection, error) {
	var conn *amqp.Connection
	err := r.retry(ctx, DebugConnection, false, func() error {
		var err error
		conn, err = createConnection(url, cfg)
		return err
	})
	if err != nil {
		return nil, err
	}
	return conn, nil
}

func createConnection(url string, cfg *amqp.Config) (*amqp.Connection, error) {
	if cfg == nil {
		return nil, errors.Join(ErrNotInitialized, fmt.Errorf("rabbitmq config is not initialized yet"))
	}
	conn, err := amqp.DialConfig(url, *cfg)
	if err != nil {
		return nil, err
	}

	return conn, nil
}

// CreateChannel implement retry logic for creating a channel with rabbitmq
func (r *Retrier) CreateChannel(ctx context.Context, conn *amqp.Connection) (*amqp.Channel, error) {
	var ch *amqp.Channel
	err := r.retry(ctx, DebugChannel, false, func() error {
		var err error
		ch, err = createChannel(conn)
		return err
	})
	if err != nil {
		return nil, err
	}
	return ch, nil
}

func createChannel(conn *amqp.Connection) (*amqp.Channel, error) {
	if conn == nil {
		return nil, errors.Join(ErrNotInitialized, fmt.Errorf("connection with rabbitmq is not initialized yet"))
	}

	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	return ch, nil
}

// SubscribeToQueue implements retry logic for subscribing to a queue with rabbitmq
func (r *Retrier) SubscribeToQueue(ctx context.Context, ch *amqp.Channel, queueName string) (<-chan amqp.Delivery, error) {
	var msgs <-chan amqp.Delivery
	err := r.retry(ctx, DebugQueue, true, func() error {
		var err error
		msgs, err = subscribeToQueue(ctx, ch, queueName)
		return err
	})
	if err != nil {
		return nil, err
	}
	return msgs, nil
}

func subscribeToQueue(ctx context.Context, ch *amqp.Channel, queueName string) (<-chan amqp.Delivery, error) {
	if ch == nil {
		return nil, errors.Join(ErrNotInitialized, fmt.Errorf("channel is not exist for rabbitmq"))
	}

	msgs, err := ch.ConsumeWithContext(ctx, queueName, "", false, false, false, false, nil)
	if err != nil {
		return nil, err
	}

	return msgs, nil
}

func (r *Retrier) Publish(ctx context.Context, ch *amqp.Channel, exchange, key string, options amqp.Publishing) error {
	err := r.retry(ctx, DebugPublish, true, func() error {
		return publish(ctx, ch, exchange, key, options)
	})
	if err != nil {
		return err
	}
	return nil
}

func publish(ctx context.Context, ch *amqp.Channel, exchange, key string, options amqp.Publishing) error {
	err := ch.PublishWithContext(ctx, exchange, key, false, false, options)
	if err != nil {
		return err
	}
	return nil
}
