package rabbitmq

import (
	"context"

	amqp "github.com/rabbitmq/amqp091-go"
)

type Publisher struct {
	ch      *amqp.Channel
	retrier *Retrier
}

func NewPublisher(ctx context.Context, conn *amqp.Connection, r *Retrier) (*Publisher, error) {
	ch, err := r.<PERSON><PERSON>hannel(ctx, conn)
	if err != nil {
		return nil, err
	}

	p := &Publisher{
		ch:      ch,
		retrier: r,
	}
	return p, nil
}

func (p *Publisher) Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
	return p.retrier.Publish(ctx, p.ch, exchangeName, key, publishing)
}
