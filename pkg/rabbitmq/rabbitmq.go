package rabbitmq

import (
	"context"
	"errors"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/core"
	"gitlab.viswalslab.com/backend/ratings/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var (
	ErrNotInitialized = errors.New("rabbitmq need to be initialized before accessing this function")
)

type RabbitMQ struct {
	Conn      *amqp.Connection
	Retrier   *Retrier
	Publisher *Publisher
}

func NewRabbitMQ(ctx context.Context, config config.Messaging) (*RabbitMQ, error) {
	cfg := &amqp.Config{
		Heartbeat: config.Heartbeat,
	}

	retrier, err := NewRetrier(config.RetryConfig)
	if err != nil {
		return nil, err
	}

	conn, err := retrier.CreateConnection(ctx, config.BrokerURL, cfg)
	if err != nil {
		return nil, err
	}

	publisher, err := NewPublisher(ctx, conn, retrier)
	if err != nil {
		return nil, err
	}

	return &RabbitMQ{
		Conn:      conn,
		Retrier:   retrier,
		Publisher: publisher,
	}, nil
}

func (r *RabbitMQ) Channel(ctx context.Context) (*amqp.Channel, error) {
	ch, err := r.Retrier.CreateChannel(ctx, r.Conn)
	if err != nil {
		return nil, err
	}
	return ch, nil
}

func (r *RabbitMQ) SubscribeToQueue(ctx context.Context, ch *amqp.Channel, queueName string) (<-chan amqp.Delivery, error) {
	msgs, err := r.Retrier.SubscribeToQueue(ctx, ch, queueName)
	if err != nil {
		return nil, err
	}

	return msgs, nil
}

func (r *RabbitMQ) HandleMessage(ctx context.Context, msgs <-chan amqp.Delivery, handler Subscriber) error {
	logger := vlog.FromContext(ctx).With(vlog.F("message handler for ", handler.QueueName()))
	defer func() {
		if r := recover(); r != nil {
			logger.Error("message handler panicked",
				vlog.F("queue_name", handler.QueueName()),
				vlog.F("recover", r),
			)
		}
	}()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()

		case msg, ok := <-msgs:
			// if a channel is closed, return error
			if !ok {
				return errors.New("listening go channel closed")
			}

			msg.CorrelationId = getCorrelationId(msg)

			err := handler.HandleMessage(ctx, msg)
			if err != nil {
				logger.Error("error processing message", vlog.F("err", err))

				err = handleError(msg, err)
				if err != nil {
					logger.Error("failed to nack message", vlog.F("error", err))
				}
				continue
			}

			logger.Info("message handled successfully")
			err = msg.Ack(false)
			if err != nil {
				logger.Error("failed to ack message", vlog.F("error", err))
			}
			continue
		}
	}
}

func handleError(msg amqp.Delivery, err error) error {
	//var requeue bool
	//
	//switch {
	//case errors.Is(err, ErrNotInitialized):
	//	requeue = false
	//case errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded):
	//	requeue = true
	//default:
	//	requeue = true
	//}

	return msg.Nack(false, false)
}

func getCorrelationId(msg amqp.Delivery) string {
	if msg.CorrelationId != "" {
		return msg.CorrelationId
	}

	return core.NewID().Value()
}

func (r *RabbitMQ) Close() error {
	err := r.Conn.Close()
	if err != nil && !errors.Is(err, amqp.ErrClosed) {
		return err
	}

	return nil
}
