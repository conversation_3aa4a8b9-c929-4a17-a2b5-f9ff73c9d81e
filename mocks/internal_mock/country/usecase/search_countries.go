// Code generated by MockGen. DO NOT EDIT.
// Source: search_countries.go
//
// Generated by this command:
//
//	mockgen -source search_countries.go -destination ../../../mocks/internal_mock/country/usecase/search_countries.go -package usecase
//

// Package usecase is a generated GoMock package.
package usecase

import (
	context "context"
	reflect "reflect"

	repository "gitlab.viswalslab.com/backend/ratings/internal/country/repository"
	gomock "go.uber.org/mock/gomock"
)

// MocksearchCountriesRepo is a mock of searchCountriesRepo interface.
type MocksearchCountriesRepo struct {
	ctrl     *gomock.Controller
	recorder *MocksearchCountriesRepoMockRecorder
	isgomock struct{}
}

// MocksearchCountriesRepoMockRecorder is the mock recorder for MocksearchCountriesRepo.
type MocksearchCountriesRepoMockRecorder struct {
	mock *MocksearchCountriesRepo
}

// NewMocksearchCountriesRepo creates a new mock instance.
func NewMocksearchCountriesRepo(ctrl *gomock.Controller) *MocksearchCountriesRepo {
	mock := &MocksearchCountriesRepo{ctrl: ctrl}
	mock.recorder = &MocksearchCountriesRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MocksearchCountriesRepo) EXPECT() *MocksearchCountriesRepoMockRecorder {
	return m.recorder
}

// SearchCountries mocks base method.
func (m *MocksearchCountriesRepo) SearchCountries(ctx context.Context, filter *repository.SearchCountryFilter) (*repository.SearchCountriesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchCountries", ctx, filter)
	ret0, _ := ret[0].(*repository.SearchCountriesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchCountries indicates an expected call of SearchCountries.
func (mr *MocksearchCountriesRepoMockRecorder) SearchCountries(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCountries", reflect.TypeOf((*MocksearchCountriesRepo)(nil).SearchCountries), ctx, filter)
}
